package com.crentsil.demo.repository;

import com.crentsil.demo.model.Student;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;

public interface  StudentRepository extends JpaRepository<Student, Long> {

    List<Student> findByName(String name);
    List<Student> findByNameContainingIgnoreCase(String name);

//    List<Student> findById(String Id);




//    List<Student> findByDOB(LocalDate DOB);



}
