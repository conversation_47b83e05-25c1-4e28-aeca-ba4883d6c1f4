package com.crentsil.demo.controller;

import com.crentsil.demo.model.Student;
import com.crentsil.demo.repository.StudentRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/students")
public class StudentController {
    private final StudentRepository studentRepository;

    @Autowired
    public StudentController(StudentRepository studentRepository) {
        this.studentRepository = studentRepository;
    }

    @PostMapping("create")
    public ResponseEntity<?> addStudent(@RequestBody Student student) {
        if (student.getName().isEmpty()){
            return ResponseEntity.badRequest().body("name should be mandetory");
        }
        System.out.println("Adding student: " + student);
        return ResponseEntity.ok(student);
    }

@PostMapping("search")
//public ResponseEntity<List<Student>> findStudentsByName(@RequestBody Student student) {
    public ResponseEntity<?> findStudentsByName(@RequestBody Student student) {
        if (student.getName() == null || student.getName().trim().isEmpty()) {
            return ResponseEntity.badRequest().build();
        }

        System.out.println("Searching for students with name: " + student.getName());
        List<Student> foundStudents = studentRepository.findByNameContainingIgnoreCase(student.getName());

        if (foundStudents.isEmpty()) {
            return ResponseEntity.badRequest().body("no students with that name");
        }

        return ResponseEntity.ok(foundStudents);
    }
}
